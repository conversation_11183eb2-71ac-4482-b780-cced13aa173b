["C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_export.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_messenger.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\icudtl.dat", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\kernel_blob.bin", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\assets/icon-promotor-blue.ico", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\assets/icon-promotor-blue.png", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\AssetManifest.json", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\AssetManifest.bin", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\FontManifest.json", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\NOTICES.Z", "C:\\TRABALHO\\PROMOBELL\\promotor\\build\\flutter_assets\\NativeAssetsManifest.json"]