^C:\TRABALHO\PROMOBELL\PROMOTOR\BUILD\WINDOWS\X64\CMAKEFILES\6F09D7767350EC33A90A932B88E63886\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\src\flutter PROJECT_DIR=C:\TRABALHO\PROMOBELL\promotor FLUTTER_ROOT=C:\src\flutter FLUTTER_EPHEMERAL_DIR=C:\TRABALHO\PROMOBELL\promotor\windows\flutter\ephemeral PROJECT_DIR=C:\TRABALHO\PROMOBELL\promotor FLUTTER_TARGET=C:\TRABALHO\PROMOBELL\promotor\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\TRABALHO\PROMOBELL\promotor\.dart_tool\package_config.json C:/src/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\TRABALHO\PROMOBELL\PROMOTOR\BUILD\WINDOWS\X64\CMAKEFILES\227EEF1E7D502A1A30EB456F68F4C626\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\TRABALHO\PROMOBELL\PROMOTOR\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/TRABALHO/PROMOBELL/promotor/windows -BC:/TRABALHO/PROMOBELL/promotor/build/windows/x64 --check-stamp-file C:/TRABALHO/PROMOBELL/promotor/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
